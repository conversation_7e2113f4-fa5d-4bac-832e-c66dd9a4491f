/**
 * Comprehensive Test Suite for withRetry Function
 *
 * Test Categories:
 * 1. Basic Functionality
 * 2. Configuration Edge Cases
 * 3. Callback Behavior
 * 4. Error Handling & Edge Cases
 * 5. Performance & Timing
 * 6. Integration Scenarios
 */

import { getRetryDelay, withRetry } from '../app/utils/sender/retry'

interface TestResult {
    name: string
    passed: boolean
    error?: string
    duration?: number
}

class RetryTester {
    private readonly results: TestResult[] = []
    private totalTests = 0
    private passedTests = 0

    async runTest(name: string, testFn: () => Promise<void>): Promise<void> {
        this.totalTests++
        const startTime = Date.now()

        try {
            await testFn()
            const duration = Date.now() - startTime
            this.results.push({ name, passed: true, duration })
            this.passedTests++
            console.log(`✅ ${name} (${duration}ms)`)
        } catch (error) {
            const duration = Date.now() - startTime
            const errorMsg = error instanceof Error ? error.message : String(error)
            this.results.push({ name, passed: false, error: errorMsg, duration })
            console.log(`❌ ${name} - ${errorMsg} (${duration}ms)`)
        }
    }

    printSummary(): void {
        console.log(`\n${'='.repeat(60)}`)
        console.log(`TEST SUMMARY: ${this.passedTests}/${this.totalTests} passed`)
        console.log('='.repeat(60))

        const failed = this.results.filter((r) => !r.passed)

        if (failed.length > 0) {
            console.log('\nFAILED TESTS:')

            failed.forEach((test) => {
                console.log(`  ❌ ${test.name}: ${test.error}`)
            })
        }

        const avgDuration = this.results.reduce((sum, r) => sum + (r.duration || 0), 0) / this.results.length
        console.log(`\nAverage test duration: ${avgDuration.toFixed(2)}ms`)
    }
}

async function runAllTests() {
    const tester = new RetryTester()

    // ===== BASIC FUNCTIONALITY =====
    console.log('\n🔧 BASIC FUNCTIONALITY TESTS')

    await tester.runTest('Success without retry', async () => {
        let callCount = 0

        const result = await withRetry(async () => {
            callCount++

            return 'success'
        }, { enabled: true, retries: 3 })

        if (result !== 'success' || callCount !== 1) {
            throw new Error(`Expected success with 1 call, got ${result} with ${callCount} calls`)
        }
    })

    await tester.runTest('Retry disabled', async () => {
        let callCount = 0

        try {
            await withRetry(async () => {
                callCount++
                throw new Error('test error')
            }, { enabled: false, retries: 3 })
        } catch (error) {
            if (callCount !== 1) {
                throw new Error(`Expected 1 call when disabled, got ${callCount}`)
            }

            if (!(error instanceof Error) || error.message !== 'test error') {
                throw new Error('Expected original error when disabled')
            }
        }
    })

    await tester.runTest('Successful retry after failures', async () => {
        let callCount = 0

        const result = await withRetry(async () => {
            callCount++

            if (callCount < 3) { throw new Error(`attempt ${callCount}`) }

            return 'success'
        }, { enabled: true, retries: 5, delay: 1 })

        if (result !== 'success' || callCount !== 3) {
            throw new Error(`Expected success after 3 calls, got ${result} after ${callCount} calls`)
        }
    })

    await tester.runTest('Max retries exceeded', async () => {
        let callCount = 0

        try {
            await withRetry(async () => {
                callCount++
                throw new Error(`attempt ${callCount}`)
            }, { enabled: true, retries: 2, delay: 1 })
        } catch (error) {
            if (callCount !== 3) { // initial + 2 retries
                throw new Error(`Expected 3 calls, got ${callCount}`)
            }

            if (!(error instanceof AggregateError)) {
                throw new TypeError('Expected AggregateError for multiple failures')
            }

            if (error.errors.length !== 3) {
                throw new Error(`Expected 3 errors in AggregateError, got ${error.errors.length}`)
            }
        }
    })

    // ===== CONFIGURATION EDGE CASES =====
    console.log('\n⚙️ CONFIGURATION EDGE CASES')

    await tester.runTest('Zero retries', async () => {
        let callCount = 0

        try {
            await withRetry(async () => {
                callCount++
                throw new Error('test error')
            }, { enabled: true, retries: 0, delay: 1 })
        } catch (error) {
            if (callCount !== 1) {
                throw new Error(`Expected 1 call with 0 retries, got ${callCount}`)
            }

            // With 0 retries, we still get AggregateError because there's 1 error collected
            if (!(error instanceof AggregateError) || error.errors.length !== 1) {
                throw new Error('Expected AggregateError with 1 error for 0 retries')
            }
        }
    })

    await tester.runTest('Negative retries (no execution)', async () => {
        let callCount = 0

        try {
            await withRetry(async () => {
                callCount++
                throw new Error('test error')
            }, { enabled: true, retries: -1, delay: 1 })
        } catch (error) {
            if (callCount !== 0) {
                throw new Error(`Expected 0 calls with negative retries, got ${callCount}`)
            }

            // With negative retries, loop doesn't execute, so we get the final AggregateError with empty errors
            if (!(error instanceof AggregateError) || error.errors.length > 0) {
                throw new Error('Expected AggregateError with 0 errors for negative retries')
            }
        }
    })

    await tester.runTest('Very large retry count', async () => {
        let callCount = 0

        const result = await withRetry(async () => {
            callCount++

            if (callCount < 2) { throw new Error('temp error') }

            return 'success'
        }, { enabled: true, retries: 1000, delay: 1 })

        if (callCount !== 2) {
            throw new Error(`Expected early success with large retry count, got ${callCount} calls`)
        }
    })

    // ===== DELAY AND TIMING =====
    console.log('\n⏱️ DELAY AND TIMING TESTS')

    await tester.runTest('getRetryDelay calculation', async () => {
        const delay1 = getRetryDelay(0, { delay: 1000, backoff: 2, jitter: 0, maxDelay: 10_000 })
        const delay2 = getRetryDelay(1, { delay: 1000, backoff: 2, jitter: 0, maxDelay: 10_000 })
        const delay3 = getRetryDelay(2, { delay: 1000, backoff: 2, jitter: 0, maxDelay: 10_000 })

        if (delay1 !== 1000 || delay2 !== 2000 || delay3 !== 4000) {
            throw new Error(`Expected exponential backoff: 1000, 2000, 4000, got ${delay1}, ${delay2}, ${delay3}`)
        }
    })

    await tester.runTest('maxDelay enforcement', async () => {
        const delay = getRetryDelay(10, { delay: 1000, backoff: 2, jitter: 0, maxDelay: 5000 })

        if (delay > 5000) {
            throw new Error(`Expected delay <= 5000, got ${delay}`)
        }
    })

    await tester.runTest('Jitter variation', async () => {
        const delay1 = getRetryDelay(1, { delay: 1000, backoff: 2, jitter: 0.1, maxDelay: 10_000 })
        const delay2 = getRetryDelay(1, { delay: 1000, backoff: 2, jitter: 0.1, maxDelay: 10_000 })

        // With jitter, delays should be different (statistically)
        // We'll run this multiple times to increase confidence
        let allSame = true

        for (let i = 0; i < 10; i++) {
            const d1 = getRetryDelay(1, { delay: 1000, backoff: 2, jitter: 0.1, maxDelay: 10_000 })
            const d2 = getRetryDelay(1, { delay: 1000, backoff: 2, jitter: 0.1, maxDelay: 10_000 })

            if (d1 !== d2) {
                allSame = false
                break
            }
        }

        if (allSame) {
            throw new Error('Jitter should produce different delays')
        }
    })

    // Continue with more tests...
    console.log('\n📞 CALLBACK BEHAVIOR TESTS')

    await tester.runTest('onFailedAttempt called correctly', async () => {
        const attempts: number[] = []
        const retriesLeft: number[] = []

        try {
            await withRetry(async () => {
                throw new Error('test error')
            }, {
                enabled: true,
                retries: 2,
                delay: 1,
                onFailedAttempt: async (error, attempt, left) => {
                    attempts.push(attempt)
                    retriesLeft.push(left)
                },
            })
        } catch {
            if (attempts.length !== 3 || !attempts.every((a, i) => a === i + 1)) {
                throw new Error(`Expected attempts [1,2,3], got [${attempts.join(',')}]`)
            }

            if (retriesLeft.length !== 3 || retriesLeft[0] !== 2 || retriesLeft[1] !== 1 || retriesLeft[2] !== 0) {
                throw new Error(`Expected retriesLeft [2,1,0], got [${retriesLeft.join(',')}]`)
            }
        }
    })

    console.log('\n🎯 RESPONSE RETRY TESTS')

    await tester.runTest('shouldRetryOnResponse with onResponseAttempt', async () => {
        let callCount = 0
        const responseAttempts: number[] = []

        const result = await withRetry(async () => {
            callCount++

            return { status: callCount < 3 ? 500 : 200 }
        }, {
            enabled: true,
            retries: 5,
            delay: 1,
            shouldRetryOnResponse: async (response) => response.status === 500,
            onResponseAttempt: async (response, attempt, left) => {
                responseAttempts.push(attempt)
            },
        })

        if (result.status !== 200 || callCount !== 3) {
            throw new Error(`Expected final status 200 after 3 calls, got ${result.status} after ${callCount}`)
        }

        if (responseAttempts.length !== 2) {
            throw new Error(`Expected onResponseAttempt called 2 times (for 500 responses), got ${responseAttempts.length}`)
        }
    })

    // ===== ERROR HANDLING EDGE CASES =====
    console.log('\n💥 ERROR HANDLING EDGE CASES')

    await tester.runTest('Callback throws error - onFailedAttempt', async () => {
        let callCount = 0

        try {
            await withRetry(async () => {
                callCount++
                throw new Error('original error')
            }, {
                enabled: true,
                retries: 3,
                delay: 1,
                onFailedAttempt: async (error, attempt) => {
                    if (attempt === 2) {
                        throw new Error('callback abort')
                    }
                },
            })
        } catch (error) {
            if (!(error instanceof Error) || error.message !== 'callback abort') {
                throw new Error('Expected callback error to abort retry')
            }

            if (callCount !== 2) {
                throw new Error(`Expected 2 calls before callback abort, got ${callCount}`)
            }
        }
    })

    await tester.runTest('Callback throws error - shouldRetry', async () => {
        let callCount = 0

        try {
            await withRetry(async () => {
                callCount++
                throw new Error('original error')
            }, {
                enabled: true,
                retries: 3,
                delay: 1,
                shouldRetry: async () => {
                    if (callCount === 2) {
                        throw new Error('shouldRetry abort')
                    }

                    return true
                },
            })
        } catch (error) {
            if (!(error instanceof Error) || error.message !== 'shouldRetry abort') {
                throw new Error('Expected shouldRetry error to abort retry')
            }
        }
    })

    await tester.runTest('Callback throws error - shouldRetryOnResponse', async () => {
        let callCount = 0

        try {
            await withRetry(async () => {
                callCount++

                return { status: 500 }
            }, {
                enabled: true,
                retries: 3,
                delay: 1,
                shouldRetryOnResponse: async () => {
                    if (callCount === 2) {
                        throw new Error('shouldRetryOnResponse abort')
                    }

                    return true
                },
            })
        } catch (error) {
            if (!(error instanceof Error) || error.message !== 'shouldRetryOnResponse abort') {
                throw new Error('Expected shouldRetryOnResponse error to abort retry')
            }
        }
    })

    await tester.runTest('Callback throws error - onResponseAttempt', async () => {
        let callCount = 0

        try {
            await withRetry(async () => {
                callCount++

                return { status: 500 }
            }, {
                enabled: true,
                retries: 3,
                delay: 1,
                shouldRetryOnResponse: async () => true,
                onResponseAttempt: async (response, attempt) => {
                    if (attempt === 2) {
                        throw new Error('onResponseAttempt abort')
                    }
                },
            })
        } catch (error) {
            if (!(error instanceof Error) || error.message !== 'onResponseAttempt abort') {
                throw new Error('Expected onResponseAttempt error to abort retry')
            }
        }
    })

    await tester.runTest('shouldRetry returns false immediately', async () => {
        let callCount = 0

        try {
            await withRetry(async () => {
                callCount++
                throw new Error('original error')
            }, {
                enabled: true,
                retries: 3,
                delay: 1,
                shouldRetry: async () => false,
            })
        } catch (error) {
            if (callCount !== 1) {
                throw new Error(`Expected 1 call when shouldRetry returns false, got ${callCount}`)
            }

            if (error instanceof AggregateError) {
                throw new TypeError('Expected single error when shouldRetry returns false immediately')
            }
        }
    })

    // ===== COMPLEX INTEGRATION SCENARIOS =====
    console.log('\n🔄 COMPLEX INTEGRATION SCENARIOS')

    await tester.runTest('Mixed error and response retries', async () => {
        let callCount = 0
        const logs: string[] = []

        const result = await withRetry(async () => {
            callCount++

            if (callCount === 1) { throw new Error('first error') }

            if (callCount === 2) { throw new Error('second error') }

            if (callCount === 3) { return { status: 500 } }

            if (callCount === 4) { return { status: 429 } }

            return { status: 200 }
        }, {
            enabled: true,
            retries: 10,
            delay: 1,
            onFailedAttempt: async (error, attempt) => {
                logs.push(`error-${attempt}`)
            },
            shouldRetry: async () => true,
            shouldRetryOnResponse: async (response) => response.status !== 200,
            onResponseAttempt: async (response, attempt) => {
                logs.push(`response-${attempt}-${response.status}`)
            },
        })

        if (result.status !== 200 || callCount !== 5) {
            throw new Error(`Expected status 200 after 5 calls, got ${result.status} after ${callCount}`)
        }

        const expectedLogs = ['error-1', 'error-2', 'response-3-500', 'response-4-429']

        if (logs.join(',') !== expectedLogs.join(',')) {
            throw new Error(`Expected logs ${expectedLogs.join(',')}, got ${logs.join(',')}`)
        }
    })

    await tester.runTest('All callbacks with async operations', async () => {
        let callCount = 0
        const delays: number[] = []

        const result = await withRetry(async () => {
            callCount++
            await new Promise((resolve) => setTimeout(resolve, 1))

            if (callCount < 3) { throw new Error(`async error ${callCount}`) }

            return 'async success'
        }, {
            enabled: true,
            retries: 5,
            delay: 1,
            onFailedAttempt: async (error, attempt) => {
                await new Promise((resolve) => setTimeout(resolve, 1))
                delays.push(attempt)
            },
            shouldRetry: async () => {
                await new Promise((resolve) => setTimeout(resolve, 1))

                return true
            },
        })

        if (result !== 'async success' || callCount !== 3) {
            throw new Error(`Expected async success after 3 calls, got ${result} after ${callCount}`)
        }

        if (delays.length !== 2) {
            throw new Error(`Expected 2 onFailedAttempt calls, got ${delays.length}`)
        }
    })

    // ===== PERFORMANCE AND EDGE CASES =====
    console.log('\n⚡ PERFORMANCE AND EDGE CASES')

    await tester.runTest('Very fast retries (0 delay)', async () => {
        let callCount = 0
        const start = Date.now()

        const result = await withRetry(async () => {
            callCount++

            if (callCount < 5) { throw new Error('fast error') }

            return 'fast success'
        }, {
            enabled: true,
            retries: 10,
            delay: 0,
            backoff: 1,
            jitter: 0,
        })

        const duration = Date.now() - start

        if (result !== 'fast success' || callCount !== 5) {
            throw new Error(`Expected fast success after 5 calls, got ${result} after ${callCount}`)
        }

        if (duration > 100) {
            throw new Error(`Expected fast execution < 100ms, took ${duration}ms`)
        }
    })

    await tester.runTest('Function returns undefined', async () => {
        let callCount = 0

        const result = await withRetry(async () => {
            callCount++
        }, { enabled: true, retries: 3 })

        if (result !== undefined || callCount !== 1) {
            throw new Error(`Expected undefined result with 1 call, got ${result} with ${callCount} calls`)
        }
    })

    await tester.runTest('Function returns null', async () => {
        let callCount = 0

        const result = await withRetry(async () => {
            callCount++

            return null
        }, { enabled: true, retries: 3 })

        if (result !== null || callCount !== 1) {
            throw new Error(`Expected null result with 1 call, got ${result} with ${callCount} calls`)
        }
    })

    await tester.runTest('Function returns falsy values', async () => {
        const falsyValues = [false, 0, '', Number.NaN]

        for (const value of falsyValues) {
            let callCount = 0

            const result = await withRetry(async () => {
                callCount++

                return value
            }, { enabled: true, retries: 3 })

            if (!Object.is(result, value) || callCount !== 1) {
                throw new Error(`Expected ${value} result with 1 call, got ${result} with ${callCount} calls`)
            }
        }
    })

    tester.printSummary()
}

// Run all tests
runAllTests().catch(console.error)
