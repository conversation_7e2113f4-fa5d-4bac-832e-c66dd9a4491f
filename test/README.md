# Retry Function Test Suite

## Overview

This comprehensive test suite validates the `withRetry` function implementation with 23 test cases covering all possible scenarios, edge cases, and error conditions.

## Test Categories

### 🔧 Basic Functionality (4 tests)
- **Success without retry**: Validates normal execution without errors
- **Retry disabled**: Ensures no retries when `enabled: false`
- **Successful retry after failures**: Tests recovery after temporary failures
- **Max retries exceeded**: Validates AggregateError with all collected errors

### ⚙️ Configuration Edge Cases (3 tests)
- **Zero retries**: Tests behavior with `retries: 0`
- **Negative retries**: Tests behavior with `retries: -1` (no execution)
- **Very large retry count**: Ensures early success doesn't waste cycles

### ⏱️ Delay and Timing (3 tests)
- **getRetryDelay calculation**: Validates exponential backoff formula
- **maxDelay enforcement**: Ensures delay caps are respected
- **Jitter variation**: Confirms randomization works correctly

### 📞 Callback Behavior (1 test)
- **onFailedAttempt called correctly**: Validates callback parameters and timing

### 🎯 Response Retry Tests (1 test)
- **shouldRetryOnResponse with onResponseAttempt**: Tests response-based retry logic

### 💥 Error Handling Edge Cases (5 tests)
- **Callback throws error - onFailedAttempt**: Tests callback error abortion
- **Callback throws error - shouldRetry**: Tests shouldRetry error handling
- **Callback throws error - shouldRetryOnResponse**: Tests response callback errors
- **Callback throws error - onResponseAttempt**: Tests response attempt callback errors
- **shouldRetry returns false immediately**: Tests early termination

### 🔄 Complex Integration Scenarios (2 tests)
- **Mixed error and response retries**: Tests complex retry flows
- **All callbacks with async operations**: Tests async callback handling

### ⚡ Performance and Edge Cases (4 tests)
- **Very fast retries (0 delay)**: Tests performance with no delays
- **Function returns undefined**: Tests undefined return values
- **Function returns null**: Tests null return values
- **Function returns falsy values**: Tests all falsy return values

## Key Test Insights

### Error Handling Behavior
- **Single error**: Throws original error when no retries occur
- **Multiple errors**: Throws `AggregateError` containing all errors
- **Callback errors**: Immediately abort retry process and throw callback error

### Retry Logic Validation
- **Zero retries**: Still executes once, throws `AggregateError` with 1 error
- **Negative retries**: No execution, throws `AggregateError` with 0 errors
- **Callback execution order**: onFailedAttempt → shouldRetry → (retry or abort)
- **Response callbacks**: onResponseAttempt only called when shouldRetryOnResponse returns true

### Performance Characteristics
- **Fast execution**: 0ms delay retries complete in <100ms
- **Jitter effectiveness**: Random delays prevent thundering herd
- **Exponential backoff**: Proper 2^n delay progression

## Running Tests

```bash
npx tsx test/retry-comprehensive.test.ts
```

## Test Results Format

```
✅ Test Name (duration)  # Passed
❌ Test Name - Error     # Failed

============================================================
TEST SUMMARY: X/Y passed
============================================================

FAILED TESTS:
  ❌ Test Name: Error message

Average test duration: X.XXms
```

## Coverage Analysis

This test suite achieves comprehensive coverage of:

- ✅ All configuration parameters
- ✅ All callback functions
- ✅ All error conditions
- ✅ All edge cases
- ✅ Performance scenarios
- ✅ Integration scenarios
- ✅ Type safety (falsy values, undefined, null)

## Professional Testing Approach

As a professional tester, this suite follows best practices:

1. **Comprehensive Coverage**: Tests all code paths and edge cases
2. **Clear Test Names**: Descriptive names explaining what's being tested
3. **Isolated Tests**: Each test is independent and focused
4. **Performance Validation**: Timing and efficiency tests
5. **Error Scenario Testing**: Comprehensive error handling validation
6. **Integration Testing**: Complex real-world scenarios
7. **Boundary Testing**: Edge cases like zero/negative values
8. **Type Safety Testing**: Various return value types

## Maintenance

When modifying the `withRetry` function:

1. Run this test suite to ensure no regressions
2. Add new tests for any new functionality
3. Update existing tests if behavior intentionally changes
4. Maintain test documentation and comments
