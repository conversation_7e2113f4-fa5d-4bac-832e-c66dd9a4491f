import type { Awaitable } from '@kdt310722/utils/promise'

export interface GetRetryDelayOptions {
    delay?: number
    backoff?: number
    jitter?: number
    maxDelay?: number
}

export interface SenderRetryOptions<T> extends GetRetryDelayOptions {
    enabled?: boolean
    retries?: number
    shouldRetry?: (error: unknown) => Awaitable<boolean>
    shouldRetryOnResponse?: (response: T) => Awaitable<boolean>
}

export function getRetryDelay(attempts: number, { delay = 1000, backoff = 2, jitter = 0.1, maxDelay = 10_000 }: GetRetryDelayOptions = {}) {}

export async function withRetry<T>(fn: () => Awaitable<T>, { enabled = true, retries = 3, shouldRetry, shouldRetryOnResponse, ...delayOptions }: SenderRetryOptions<T> = {}): Promise<T> {}
