import { isNullish } from '@kdt310722/utils/common'
import { minMax, random } from '@kdt310722/utils/number'
import { type Awaitable, sleep } from '@kdt310722/utils/promise'

export interface GetRetryDelayOptions {
    delay?: number
    backoff?: number
    jitter?: number
    maxDelay?: number
}

export interface SenderRetryOptions<T> extends GetRetryDelayOptions {
    enabled?: boolean
    retries?: number
    shouldRetry?: (error: unknown) => Awaitable<boolean>
    shouldRetryOnResponse?: (response: T) => Awaitable<boolean>
}

export function getRetryDelay(attempts: number, { delay = 1000, backoff = 2, jitter = 0.1, maxDelay = 10_000 }: GetRetryDelayOptions = {}) {
    const exponentialDelay = delay * backoff ** attempts
    const jitterAmount = exponentialDelay * jitter
    const jitterRange = random(-jitterAmount, jitterAmount)
    const finalDelay = exponentialDelay + jitterRange

    return minMax(finalDelay, 0, maxDelay)
}

export async function withRetry<T>(fn: () => Awaitable<T>, { enabled = true, retries = 3, shouldRetry, shouldRetryOnResponse, ...delayOptions }: SenderRetryOptions<T> = {}): Promise<T> {
    if (!enabled) {
        return await fn()
    }

    const errors: unknown[] = []
    let attempt = 0

    while (attempt <= retries) {
        try {
            const result = await fn()

            if (!isNullish(shouldRetryOnResponse) && await shouldRetryOnResponse(result) && attempt < retries) {
                await sleep(getRetryDelay(attempt, delayOptions))
                attempt++
                continue
            }

            return result
        } catch (error) {
            errors.push(error)

            if (!isNullish(shouldRetry) && !(await shouldRetry(error))) {
                throw errors.length === 1 ? error : new AggregateError(errors, `Failed after ${attempt + 1} attempts`)
            }

            if (attempt < retries) {
                await sleep(getRetryDelay(attempt, delayOptions))
                attempt++
                continue
            }

            throw new AggregateError(errors, `Failed after ${attempt + 1} attempts`)
        }
    }

    throw new AggregateError(errors, `Failed after ${attempt} attempts`)
}
