import { isNullish } from '@kdt310722/utils/common'
import { minMax, random } from '@kdt310722/utils/number'
import { type Awaitable, sleep } from '@kdt310722/utils/promise'

export interface GetRetryDelayOptions {
    delay?: number
    backoff?: number
    jitter?: number
    maxDelay?: number
}

export interface SenderRetryOptions<T> extends GetRetryDelayOptions {
    enabled?: boolean
    retries?: number
    shouldRetry?: (error: unknown) => Awaitable<boolean>
    shouldRetryOnResponse?: (response: T) => Awaitable<boolean>
}

export function getRetryDelay(attempts: number, { delay = 1000, backoff = 2, jitter = 0.1, maxDelay = 10_000 }: GetRetryDelayOptions = {}) {
    const exponentialDelay = delay * backoff ** attempts
    const jitterAmount = exponentialDelay * jitter
    const jitterRange = random(-jitterAmount, jitterAmount)
    const finalDelay = exponentialDelay + jitterRange

    return minMax(finalDelay, 0, maxDelay)
}

export async function withRetry<T>(fn: () => Awaitable<T>, { enabled = true, retries = 3, shouldRetry, shouldRetryOnResponse, ...delayOptions }: SenderRetryOptions<T> = {}): Promise<T> {
    if (!enabled) {
        return await fn()
    }

    let lastError: unknown
    let attempts = 0

    while (attempts <= retries) {
        try {
            const result = await fn()

            if (!isNullish(shouldRetryOnResponse) && await shouldRetryOnResponse(result) && attempts < retries) {
                attempts++
                await sleep(getRetryDelay(attempts - 1, delayOptions))
                continue
            }

            return result
        } catch (error) {
            lastError = error

            if (!isNullish(shouldRetry) && !(await shouldRetry(error))) {
                throw error
            }

            if (attempts < retries) {
                attempts++
                await sleep(getRetryDelay(attempts - 1, delayOptions))
                continue
            }

            throw error
        }
    }

    throw lastError
}
